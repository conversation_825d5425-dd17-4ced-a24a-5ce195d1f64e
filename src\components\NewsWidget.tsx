import { useState, useRef, useCallback, useEffect, useMemo } from 'preact/hooks';
import { usePressreleaseData } from "../hooks/usePressreleaseData";
import type { FC, UIEvent } from 'preact/compat';

interface PressRelease {
  node: {
    title: string;
    dateTime: string;
    __typename: string;
  };
  cursor: string;
  __typename: string;
}

interface VirtualScrollProps {
  items: PressRelease[];
  itemHeight: number;
  containerHeight: number;
  onLoadMore?: () => void;
  hasNextPage?: boolean;
  loading?: boolean;
}

const VirtualScroll: FC<VirtualScrollProps> = ({
  items,
  itemHeight,
  containerHeight,
  onLoadMore,
  hasNextPage,
  loading
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  const scrollElementRef = useRef<HTMLDivElement>(null);
  const loadMoreTimeoutRef = useRef<number | null>(null);

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (loadMoreTimeoutRef.current) {
        clearTimeout(loadMoreTimeoutRef.current);
      }
    };
  }, []);

  const visibleStart = Math.floor(scrollTop / itemHeight);
  const visibleEnd = Math.min(
    visibleStart + Math.ceil(containerHeight / itemHeight) + 1,
    items.length
  );

  const totalHeight = items.length * itemHeight;
  const offsetY = visibleStart * itemHeight;

  // Memoize visible items to prevent unnecessary re-renders
  const visibleItems = useMemo(() => {
    return items.slice(visibleStart, visibleEnd);
  }, [items, visibleStart, visibleEnd]);

  // Debounced load more function with proper cleanup
  const debouncedLoadMore = useCallback(() => {
    console.log('debouncedLoadMore called');
    
    if (loadMoreTimeoutRef.current) {
      clearTimeout(loadMoreTimeoutRef.current);
      console.log('Cleared previous timeout');
    }
    
    loadMoreTimeoutRef.current = window.setTimeout(() => {
      console.log('Debounce timeout executed, checking conditions:', {
        onLoadMore: !!onLoadMore,
        hasNextPage,
        loading
      });
      
      if (onLoadMore && hasNextPage && !loading) {
        console.log('Calling onLoadMore from debounced function');
        onLoadMore();
      } else {
        console.log('onLoadMore not called, conditions not met');
      }
      loadMoreTimeoutRef.current = null;
    }, 100);
  }, [onLoadMore, hasNextPage, loading]);

  const handleScroll = useCallback((e: UIEvent<HTMLDivElement>) => {
    const element = e.currentTarget;
    const scrollTop = element.scrollTop;
    const scrollHeight = element.scrollHeight;
    const clientHeight = element.clientHeight;
    
    setScrollTop(scrollTop);

    // Calculate distance from bottom
    const scrollBottom = scrollTop + clientHeight;
    const distanceFromBottom = scrollHeight - scrollBottom;
    
    // Trigger load more when close to bottom
    const threshold = itemHeight * 2; // Load when 2 items away from bottom
    const scrollPercentage = scrollBottom / scrollHeight;
    
    console.log('Scroll event:', {
      scrollTop,
      scrollHeight,
      clientHeight,
      scrollBottom,
      distanceFromBottom,
      scrollPercentage,
      threshold,
      hasNextPage,
      loading
    });

    // Load more when near the end
    const shouldTrigger = distanceFromBottom <= threshold || scrollPercentage >= 0.8;
    
    console.log('Load more conditions:', {
      hasNextPage,
      loading,
      onLoadMore: !!onLoadMore,
      shouldTrigger,
      distanceFromBottom,
      threshold,
      scrollPercentage,
      finalCondition: hasNextPage && !loading && onLoadMore && shouldTrigger
    });
    
    if (hasNextPage && !loading && onLoadMore && shouldTrigger) {
      console.log('About to call debouncedLoadMore');
      debouncedLoadMore();
    }
  }, [hasNextPage, loading, debouncedLoadMore, itemHeight]);

  const formatDate = useCallback((dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  }, []);

  return (
    <div
      ref={scrollElementRef}
      style={{
        height: containerHeight,
        overflow: 'auto',
        border: '1px solid #e0e0e0',
        borderRadius: '8px'
      }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${offsetY}px)` }}>
          {visibleItems.map((item: PressRelease, index: number) => {
            const actualIndex = visibleStart + index;
            const isEven = actualIndex % 2 === 0;
            return (
              <div
                key={item.cursor}
                style={{
                  height: itemHeight,
                  padding: '12px 16px',
                  borderBottom: '1px solid #f0f0f0',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  backgroundColor: isEven ? '#ffffff' : '#f9f9f9',
                  cursor: 'pointer',
                  transition: 'background-color 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#e3f2fd';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = isEven ? '#ffffff' : '#f9f9f9';
                }}
              >
                <div
                  style={{
                    fontSize: '14px',
                    fontWeight: '500',
                    color: '#333',
                    marginBottom: '4px',
                    lineHeight: '1.4',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    display: '-webkit-box',
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: 'vertical'
                  }}
                  title={item.node.title}
                >
                  {item.node.title}
                </div>
                <div
                  style={{
                    fontSize: '12px',
                    color: '#666',
                    fontWeight: '400'
                  }}
                >
                  {formatDate(item.node.dateTime)}
                </div>
              </div>
            );
          })}
          {loading && (
            <div
              style={{
                height: itemHeight,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: '#666',
                fontSize: '14px'
              }}
            >
              Loading more...
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

interface NewsWidgetProps {
  instrumentId: string | null;
}

const NewsWidget: FC<NewsWidgetProps> = ({ instrumentId }) => {
  const parsedInstrumentId = useMemo(() => {
    const parsed = parseInt(instrumentId || "0");
    return isNaN(parsed) ? 0 : parsed;
  }, [instrumentId]);

  const { data, pageInfo, loading, loadingMore, error, loadMore } = usePressreleaseData(parsedInstrumentId);

  const handleLoadMore = useCallback(() => {
    console.log('handleLoadMore called with:', {
      hasNextPage: pageInfo?.hasNextPage,
      loadingMore,
      dataLength: data.length,
      pageInfo
    });
    
    if (pageInfo?.hasNextPage && !loadingMore) {
      console.log('Calling loadMore from handleLoadMore');
      loadMore();
    } else {
      console.log('loadMore not called from handleLoadMore:', {
        hasNextPage: pageInfo?.hasNextPage,
        loadingMore,
        reason: !pageInfo?.hasNextPage ? 'No next page' : 'Already loading'
      });
    }
  }, [pageInfo?.hasNextPage, loadingMore, loadMore]);

  // Debug pageInfo changes
  useEffect(() => {
    console.log('NewsWidget pageInfo changed:', pageInfo);
  }, [pageInfo]);

  // Debug data changes
  useEffect(() => {
    console.log('NewsWidget data changed:', data.length, 'items');
  }, [data]);

  // Handle error state
  if (error) {
    return (
      <div style={{ 
        display: 'flex', 
        flexDirection: 'column',
        alignItems: 'center', 
        justifyContent: 'center', 
        height: '200px',
        fontSize: '14px',
        color: '#d32f2f',
        padding: '16px',
        textAlign: 'center'
      }}>
        <div style={{ marginBottom: '8px', fontWeight: '500' }}>
          Error loading press releases
        </div>
        <div style={{ fontSize: '12px', color: '#666' }}>
          {error}
        </div>
      </div>
    );
  }

  // Handle invalid instrument ID
  if (parsedInstrumentId === 0) {
    return (
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center', 
        height: '200px',
        fontSize: '14px',
        color: '#666'
      }}>
        Please select an instrument to view press releases
      </div>
    );
  }

  // Handle loading state
  if (loading && !data.length) {
    return (
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center', 
        height: '200px',
        fontSize: '14px',
        color: '#666'
      }}>
        Loading press releases...
      </div>
    );
  }

  // Handle empty state
  if (!data.length && !loading) {
    return (
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center', 
        height: '200px',
        fontSize: '14px',
        color: '#666'
      }}>
        No press releases available
      </div>
    );
  }

  return (
    <div style={{ padding: '16px' }}>
      <h3 style={{ 
        margin: '0 0 16px 0', 
        fontSize: '18px', 
        fontWeight: '600', 
        color: '#333' 
      }}>
        Press Releases ({data.length} items)
      </h3>
      <VirtualScroll
        items={data}
        itemHeight={80}
        containerHeight={400}
        onLoadMore={handleLoadMore}
        hasNextPage={pageInfo?.hasNextPage}
        loading={loadingMore}
      />
    </div>
  );
};

export default NewsWidget;