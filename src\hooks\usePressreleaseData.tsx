import { useState, useCallback, useEffect, useRef } from "preact/hooks";
import { useQuery } from "urql";
import { PRESS_RELEASES_BY_INSTRUMENT_ID_QUERY } from "../services/graphql/queries";

interface PressRelease {
  node: {
    title: string;
    dateTime: string;
    __typename: string;
  };
  cursor: string;
  __typename: string;
}

interface PageInfo {
  hasNextPage: boolean;
  endCursor: string | null;
}

export const usePressreleaseData = (instrumentId: number) => {
  const [allData, setAllData] = useState<PressRelease[]>([]);
  const [endCursor, setEndCursor] = useState<string | null>(null);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const loadingRef = useRef(false);
  const currentInstrumentId = useRef<number>(instrumentId);

  // Reset state when instrumentId changes
  useEffect(() => {
    if (currentInstrumentId.current !== instrumentId) {
      console.log('Instrument ID changed, resetting state:', currentInstrumentId.current, '->', instrumentId);
      currentInstrumentId.current = instrumentId;
      setAllData([]);
      setEndCursor(null);
      setHasNextPage(false);
      setIsLoadingMore(false);
      loadingRef.current = false;
    }
  }, [instrumentId]);

  const [result, executeQuery] = useQuery({
    query: PRESS_RELEASES_BY_INSTRUMENT_ID_QUERY,
    variables: {
      id: instrumentId,
      first: 10,
      after: null,
    },
    pause: instrumentId === 0, // Don't execute if instrumentId is 0
  });

  const { data: pressReleaseDatas, fetching } = result;

  // Initialize data on first load
  useEffect(() => {
    if (pressReleaseDatas && !fetching) {
      const edges = pressReleaseDatas?.instrumentById?.company?.pressReleases?.edges || [];
      const pageInfo = pressReleaseDatas?.instrumentById?.company?.pressReleases?.pageInfo;

      console.log('Initial data loaded:', edges.length, 'items');
      console.log('Page info:', pageInfo);

      setAllData(edges);
      setEndCursor(pageInfo?.endCursor || null);
      setHasNextPage(pageInfo?.hasNextPage || false);
    }
  }, [pressReleaseDatas, fetching]);

  const loadMore = useCallback(async () => {
    console.log('loadMore called:', { 
      hasNextPage, 
      isLoadingMore, 
      endCursor, 
      loadingRef: loadingRef.current,
      instrumentId 
    });

    // Prevent multiple concurrent loads and validate conditions
    if (!hasNextPage || isLoadingMore || !endCursor || loadingRef.current || instrumentId === 0) {
      console.log('loadMore blocked:', { 
        hasNextPage, 
        isLoadingMore, 
        endCursor, 
        loadingRef: loadingRef.current,
        instrumentId 
      });
      return;
    }

    console.log('Loading more data with cursor:', endCursor);
    setIsLoadingMore(true);
    loadingRef.current = true;

    try {
      const result = await executeQuery({
        id: instrumentId,
        first: 10,
        after: endCursor,
      });

      console.log('Load more result:', result);

      console.log('result.data', result);
      if (result.data) {
        const newEdges = result.data.instrumentById?.company?.pressReleases?.edges || [];
        const pageInfo = result.data.instrumentById?.company?.pressReleases?.pageInfo;

        console.log('New edges loaded:', newEdges.length);
        console.log('New page info:', pageInfo);

        // Check for duplicates before adding
        setAllData(prev => {
          const existingCursors = new Set(prev.map(item => item.cursor));
          const filteredNewEdges = newEdges.filter((edge: PressRelease) => !existingCursors.has(edge.cursor));
          const updated = [...prev, ...filteredNewEdges];
          console.log('Total items after load more:', updated.length);
          return updated;
        });

        setEndCursor(pageInfo?.endCursor || null);
        setHasNextPage(pageInfo?.hasNextPage || false);
      }
    } catch (error) {
      console.error('Error loading more data:', error);
    } finally {
      setIsLoadingMore(false);
      loadingRef.current = false;
    }
  }, [hasNextPage, isLoadingMore, endCursor, instrumentId, executeQuery]);

  return {
    data: allData,
    pageInfo: { hasNextPage, endCursor } as PageInfo,
    loading: fetching && allData.length === 0,
    loadingMore: isLoadingMore,
    loadMore
  };
};