import { useState, useCallback, useEffect, useRef } from "preact/hooks";
import { useQuery, useClient } from "urql";
import { PRESS_RELEASES_BY_INSTRUMENT_ID_QUERY } from "../services/graphql/queries";

interface PressRelease {
  node: {
    title: string;
    dateTime: string;
    __typename: string;
  };
  cursor: string;
  __typename: string;
}

interface PageInfo {
  hasNextPage: boolean;
  endCursor: string | null;
}

export const usePressreleaseData = (instrumentId: number) => {
  const [allData, setAllData] = useState<PressRelease[]>([]);
  const [endCursor, setEndCursor] = useState<string | null>(null);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const loadingRef = useRef(false);
  const currentInstrumentId = useRef<number>(instrumentId);
  const client = useClient();

  // Reset state when instrumentId changes
  useEffect(() => {
    if (currentInstrumentId.current !== instrumentId) {
      console.log('Instrument ID changed, resetting state:', currentInstrumentId.current, '->', instrumentId);
      currentInstrumentId.current = instrumentId;
      setAllData([]);
      setEndCursor(null);
      setHasNextPage(false);
      setIsLoadingMore(false);
      setError(null);
      loadingRef.current = false;
    }
  }, [instrumentId]);

  const [result, executeQuery] = useQuery({
    query: PRESS_RELEASES_BY_INSTRUMENT_ID_QUERY,
    variables: {
      id: instrumentId,
      first: 10,
      after: null,
    },
    pause: instrumentId === 0, // Don't execute if instrumentId is 0
  });

  const { data: pressReleaseData, fetching, error: queryError } = result;

  // Handle query errors
  useEffect(() => {
    if (queryError) {
      console.error('Query error:', queryError);
      setError(queryError.message || 'Failed to load press releases');
    } else {
      setError(null);
    }
  }, [queryError]);

  // Initialize data on first load
  useEffect(() => {
    if (pressReleaseData && !fetching) {
      const edges = pressReleaseData?.instrumentById?.company?.pressReleases?.edges || [];
      const pageInfo = pressReleaseData?.instrumentById?.company?.pressReleases?.pageInfo;

      console.log('Initial data loaded:', edges.length, 'items');
      console.log('Page info:', pageInfo);

      setAllData(edges);
      setEndCursor(pageInfo?.endCursor || null);
      setHasNextPage(pageInfo?.hasNextPage || false);
    }
  }, [pressReleaseData, fetching]);

  const loadMore = useCallback(async () => {
    console.log('loadMore called:', {
      hasNextPage,
      isLoadingMore,
      endCursor,
      loadingRef: loadingRef.current,
      instrumentId
    });

    // Prevent multiple concurrent loads and validate conditions
    if (!hasNextPage || isLoadingMore || !endCursor || loadingRef.current || instrumentId === 0) {
      console.log('loadMore blocked:', {
        hasNextPage,
        isLoadingMore,
        endCursor,
        loadingRef: loadingRef.current,
        instrumentId
      });
      return;
    }

    console.log('Loading more data with cursor:', endCursor);
    setIsLoadingMore(true);
    loadingRef.current = true;

    try {
      const result = await client.query(PRESS_RELEASES_BY_INSTRUMENT_ID_QUERY, {
        id: instrumentId,
        first: 10,
        after: endCursor,
      }).toPromise();

      console.log('Load more result:', result);

      if (result.data) {
        const newEdges = result.data.instrumentById?.company?.pressReleases?.edges || [];
        const pageInfo = result.data.instrumentById?.company?.pressReleases?.pageInfo;

        console.log('New edges loaded:', newEdges.length);
        console.log('New page info:', pageInfo);

        // Check for duplicates before adding
        setAllData(prev => {
          const existingCursors = new Set(prev.map(item => item.cursor));
          const filteredNewEdges = newEdges.filter((edge: PressRelease) => !existingCursors.has(edge.cursor));
          const updated = [...prev, ...filteredNewEdges];
          console.log('Total items after load more:', updated.length);
          return updated;
        });

        setEndCursor(pageInfo?.endCursor || null);
        setHasNextPage(pageInfo?.hasNextPage || false);
      }

      if (result.error) {
        console.error('Load more error:', result.error);
        setError(result.error.message || 'Failed to load more data');
      }
    } catch (error) {
      console.error('Error loading more data:', error);
      setError('Failed to load more data');
    } finally {
      setIsLoadingMore(false);
      loadingRef.current = false;
    }
  }, [hasNextPage, isLoadingMore, endCursor, instrumentId, client]);



  return {
    data: allData,
    pageInfo: { hasNextPage, endCursor } as PageInfo,
    loading: fetching && allData.length === 0,
    loadingMore: isLoadingMore,
    error,
    loadMore
  };
};